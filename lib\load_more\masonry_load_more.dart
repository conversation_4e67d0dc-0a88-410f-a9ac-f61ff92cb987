// masonry_load_more.dart - Heavily optimized (many times) by <PERSON> 4.

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

typedef FutureCallBack = Future<bool> Function();

enum MasonryLoadMoreStatusState { idle, loading, failed, finished }

class MasonryLoadMoreStatusText {
  static const String idle = 'Scroll to load more';
  static const String loading = 'Loading...';
  static const String failed = 'Failed to load items';
  static const String finished = 'No more items';

  static String getText(MasonryLoadMoreStatusState state) {
    switch (state) {
      case MasonryLoadMoreStatusState.loading:
        return loading;
      case MasonryLoadMoreStatusState.failed:
        return failed;
      case MasonryLoadMoreStatusState.finished:
        return finished;
      default:
        return idle;
    }
  }
}

class MasonryLoadMoreLoadingWidgetDefaultOpts {
  static const double containerHeight = 60.0;
  static const double size = 24.0;
  static const double strokeWidth = 3.0;
  static const Color color = Colors.blue;
  static const int scrollThreshold = 200;
  static const int debounceMilliseconds = 500;
}

class MasonryLoadMore extends StatefulWidget {
  final GlobalKey? masonryKey;

  /// Optional external scroll controller.
  ///
  /// If supplied, you must not dispose it manually while the widget is active.
  /// Let MasonryLoadMore manage the listener lifecycle.
  final ScrollController? scrollController;

  final double loadingWidgetContainerHeight;
  final double loadingWidgetSize;
  final double loadingWidgetStrokeWidth;
  final Color loadingWidgetColor;
  final int scrollThreshold;
  final int debounceMilliseconds;
  final String idleStatusText;
  final String loadingStatusText;
  final String failedStatusText;
  final String finishedStatusText;
  final bool isFinished;
  final bool runOnEmptyResult;
  final bool showLoadMoreIndicator;
  final bool enableAutoLoadMore;
  final FutureCallBack onLoadMore;
  final ScrollPhysics? physics;
  final EdgeInsets? padding;
  final int crossAxisCount;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final int itemsCount;
  final Widget Function(BuildContext, int) itemBuilder;

  const MasonryLoadMore({
    super.key,
    this.masonryKey,
    this.scrollController,
    this.loadingWidgetContainerHeight =
        MasonryLoadMoreLoadingWidgetDefaultOpts.containerHeight,
    this.loadingWidgetSize = MasonryLoadMoreLoadingWidgetDefaultOpts.size,
    this.loadingWidgetStrokeWidth =
        MasonryLoadMoreLoadingWidgetDefaultOpts.strokeWidth,
    this.loadingWidgetColor = MasonryLoadMoreLoadingWidgetDefaultOpts.color,
    this.scrollThreshold =
        MasonryLoadMoreLoadingWidgetDefaultOpts.scrollThreshold,
    this.debounceMilliseconds =
        MasonryLoadMoreLoadingWidgetDefaultOpts.debounceMilliseconds,
    this.idleStatusText = MasonryLoadMoreStatusText.idle,
    this.loadingStatusText = MasonryLoadMoreStatusText.loading,
    this.failedStatusText = MasonryLoadMoreStatusText.failed,
    this.finishedStatusText = MasonryLoadMoreStatusText.finished,
    this.isFinished = false,
    this.runOnEmptyResult = false,
    this.showLoadMoreIndicator = true,
    this.enableAutoLoadMore = true,
    required this.onLoadMore,
    this.physics,
    this.padding,
    required this.crossAxisCount,
    required this.mainAxisSpacing,
    required this.crossAxisSpacing,
    required this.itemsCount,
    required this.itemBuilder,
  });

  @override
  MasonryLoadMoreState createState() => MasonryLoadMoreState();
}

class MasonryLoadMoreState extends State<MasonryLoadMore> {
  late ScrollController _activeScrollController;
  ScrollController? _internalScrollController;
  MasonryLoadMoreStatusState _status = MasonryLoadMoreStatusState.idle;
  Timer? _debounceTimer;
  bool _isDisposed = false;
  bool _isLoadingInProgress = false;

  // Track empty load more systematically
  bool _hasTriggeredEmptyLoad = false;
  int _lastItemCount = 0;

  // Cached computed values with proper invalidation
  bool _shouldShowLoadMore = false;
  int _totalItemCount = 0;
  late int _cachedItemCount;
  late bool _cachedIsFinished;
  late bool _cachedShowLoadMoreIndicator;
  late MasonryLoadMoreStatusState _cachedStatus;

  // Track listener state more accurately
  bool _hasScrollListener = false;

  @override
  void initState() {
    super.initState();
    _lastItemCount = widget.itemsCount;
    _initializeScrollController();
    _updateAllCachedValues();
    _scheduleEmptyLoadIfNeeded();
  }

  void _initializeScrollController() {
    if (widget.scrollController != null) {
      _activeScrollController = widget.scrollController!;
      _internalScrollController = null;
    } else {
      _internalScrollController = ScrollController();
      _activeScrollController = _internalScrollController!;
    }

    _updateScrollListener();
  }

  void _updateScrollListener() {
    final shouldHaveListener = widget.enableAutoLoadMore;

    if (shouldHaveListener && !_hasScrollListener) {
      _activeScrollController.addListener(_scrollListener);
      _hasScrollListener = true;
    } else if (!shouldHaveListener && _hasScrollListener) {
      _removeScrollListener();
    }
  }

  void _removeScrollListener() {
    if (_hasScrollListener) {
      try {
        _activeScrollController.removeListener(_scrollListener);
      } catch (e) {
        debugPrint('Could not remove scroll listener: $e');
      } finally {
        _hasScrollListener = false;
      }
    }
  }

  void _updateAllCachedValues() {
    _cachedItemCount = widget.itemsCount;
    _cachedIsFinished = widget.isFinished;
    _cachedShowLoadMoreIndicator = widget.showLoadMoreIndicator;
    _cachedStatus = _status;
    _computeCachedValues();
  }

  void _computeCachedValues() {
    _shouldShowLoadMore =
        _cachedShowLoadMoreIndicator &&
        !_cachedIsFinished &&
        _cachedStatus != MasonryLoadMoreStatusState.finished;

    _totalItemCount = _cachedItemCount + (_shouldShowLoadMore ? 1 : 0);
  }

  bool _shouldInvalidateCache() {
    return _cachedItemCount != widget.itemsCount ||
        _cachedIsFinished != widget.isFinished ||
        _cachedShowLoadMoreIndicator != widget.showLoadMoreIndicator ||
        _cachedStatus != _status;
  }

  void _scheduleEmptyLoadIfNeeded() {
    if (widget.itemsCount == 0 &&
        widget.runOnEmptyResult &&
        !widget.isFinished &&
        !_hasTriggeredEmptyLoad) {
      _hasTriggeredEmptyLoad = true;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && !_isDisposed) {
          _triggerLoadMore();
        }
      });
    }
  }

  @override
  void didUpdateWidget(MasonryLoadMore oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Handle scroll controller changes safely
    _handleScrollControllerChange(oldWidget);

    // Handle auto load more changes
    if (oldWidget.enableAutoLoadMore != widget.enableAutoLoadMore) {
      _updateScrollListener();
    }

    // Invalidate cache when dependencies change
    if (_shouldInvalidateCache()) {
      _updateAllCachedValues();
    }

    // Update status if isFinished changed
    if (oldWidget.isFinished != widget.isFinished && widget.isFinished) {
      _safeUpdateStatus(MasonryLoadMoreStatusState.finished);
    }

    // Reset empty load trigger when item count changes from 0 to something else
    if (_lastItemCount == 0 && widget.itemsCount > 0) {
      _hasTriggeredEmptyLoad = false;
    }
    _lastItemCount = widget.itemsCount;
  }

  void _handleScrollControllerChange(MasonryLoadMore oldWidget) {
    final oldIsExternal = oldWidget.scrollController != null;
    final newIsExternal = widget.scrollController != null;
    final controllerChanged =
        oldWidget.scrollController != widget.scrollController;

    if (oldIsExternal != newIsExternal ||
        (newIsExternal && controllerChanged)) {
      // Remove listener from old controller
      _removeScrollListener();

      // Clean up internal controller if switching away from it
      if (!newIsExternal && _internalScrollController != null) {
        _internalScrollController!.dispose();
        _internalScrollController = null;
      }

      // Set up new controller
      if (newIsExternal) {
        _activeScrollController = widget.scrollController!;
        _internalScrollController = null;
      } else {
        _internalScrollController = ScrollController();
        _activeScrollController = _internalScrollController!;
      }

      // Add listener to new controller
      _updateScrollListener();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _cancelTimer();
    _removeScrollListener();

    // Only dispose internal controller
    if (_internalScrollController != null) {
      _internalScrollController!.dispose();
    }

    super.dispose();
  }

  void _cancelTimer() {
    final timer = _debounceTimer;
    if (timer != null && timer.isActive) {
      timer.cancel();
    }
    _debounceTimer = null;
  }

  void _scrollListener() {
    if (_isDisposed || _isLoadingInProgress || widget.isFinished) {
      return;
    }

    if (!_activeScrollController.hasClients) {
      return;
    }

    final position = _activeScrollController.position;
    if (!position.hasContentDimensions || !position.hasPixels) {
      return;
    }

    final maxScroll = position.maxScrollExtent;
    final currentScroll = position.pixels;
    final threshold = widget.scrollThreshold.toDouble();

    if (maxScroll > 0 &&
        currentScroll >= (maxScroll - threshold) &&
        _status == MasonryLoadMoreStatusState.idle) {
      _triggerLoadMore();
    }
  }

  void _triggerLoadMore() {
    if (_isDisposed || _isLoadingInProgress || widget.isFinished) {
      return;
    }

    // Cancel existing timer
    _cancelTimer();

    _debounceTimer = Timer(
      Duration(milliseconds: widget.debounceMilliseconds),
      () {
        // Don't nullify timer here - let _cancelTimer handle it
        if (!_isDisposed &&
            mounted &&
            !_isLoadingInProgress &&
            !widget.isFinished) {
          _performLoadMore();
        }
      },
    );
  }

  Future<void> _performLoadMore() async {
    if (_isDisposed || _isLoadingInProgress || widget.isFinished) {
      return;
    }

    _isLoadingInProgress = true;
    _safeUpdateStatus(MasonryLoadMoreStatusState.loading);

    try {
      final result = await widget.onLoadMore();

      if (!_isDisposed && mounted) {
        final newStatus = result
            ? MasonryLoadMoreStatusState.idle
            : MasonryLoadMoreStatusState.failed;

        _isLoadingInProgress = false;
        _safeUpdateStatus(newStatus);
      }
    } catch (e) {
      debugPrint('MasonryLoadMore error: $e');

      if (!_isDisposed && mounted) {
        _isLoadingInProgress = false;
        _safeUpdateStatus(MasonryLoadMoreStatusState.failed);
      }
    }
  }

  void _safeUpdateStatus(MasonryLoadMoreStatusState newStatus) {
    if (_isDisposed || !mounted || _status == newStatus) {
      return;
    }

    setState(() {
      _status = newStatus;
      // Update cache immediately within setState
      _cachedStatus = newStatus;
      _computeCachedValues();
    });
  }

  /// Public method to manually trigger load more
  void loadMore() {
    if (!_isDisposed &&
        !_isLoadingInProgress &&
        _status != MasonryLoadMoreStatusState.loading &&
        !widget.isFinished) {
      _triggerLoadMore();
    }
  }

  /// Public method to retry loading after failure
  void retry() {
    if (!_isDisposed &&
        !_isLoadingInProgress &&
        _status == MasonryLoadMoreStatusState.failed) {
      _triggerLoadMore();
    }
  }

  /// Public method to reset the load more state
  void reset() {
    if (!_isDisposed && !_isLoadingInProgress) {
      _hasTriggeredEmptyLoad = false;
      _safeUpdateStatus(MasonryLoadMoreStatusState.idle);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Ensure cache is up to date before building
    if (_shouldInvalidateCache()) {
      _updateAllCachedValues();
    }

    return _buildMasonry();
  }

  Widget _buildMasonry() {
    // Handle empty items case
    if (widget.itemsCount == 0) {
      if (widget.runOnEmptyResult && widget.showLoadMoreIndicator) {
        return Center(child: _buildLoadMoreView());
      }
      return const SizedBox.shrink();
    }

    return MasonryGridView.count(
      key: widget.masonryKey,
      controller: _activeScrollController,
      physics: widget.physics,
      padding: widget.padding,
      crossAxisCount: widget.crossAxisCount,
      mainAxisSpacing: widget.mainAxisSpacing,
      crossAxisSpacing: widget.crossAxisSpacing,
      itemBuilder: (context, index) {
        // Regular item
        if (index < widget.itemsCount) {
          return widget.itemBuilder(context, index);
        }

        // Load more indicator item
        return _shouldShowLoadMore
            ? _buildLoadMoreView()
            : const SizedBox.shrink();
      },
      itemCount: _totalItemCount,
    );
  }

  Widget _buildLoadMoreView() {
    final currentStatus = widget.isFinished
        ? MasonryLoadMoreStatusState.finished
        : _status;

    return MasonryLoadMoreView(
      status: currentStatus,
      containerHeight: widget.loadingWidgetContainerHeight,
      size: widget.loadingWidgetSize,
      strokeWidth: widget.loadingWidgetStrokeWidth,
      color: widget.loadingWidgetColor,
      idleStatusText: widget.idleStatusText,
      loadingStatusText: widget.loadingStatusText,
      failedStatusText: widget.failedStatusText,
      finishedStatusText: widget.finishedStatusText,
      onRetry: retry,
    );
  }
}

class MasonryLoadMoreView extends StatelessWidget {
  final MasonryLoadMoreStatusState status;
  final double containerHeight;
  final double size;
  final double strokeWidth;
  final Color color;
  final String idleStatusText;
  final String loadingStatusText;
  final String failedStatusText;
  final String finishedStatusText;
  final VoidCallback? onRetry;

  const MasonryLoadMoreView({
    super.key,
    required this.status,
    required this.containerHeight,
    required this.size,
    required this.strokeWidth,
    required this.color,
    required this.idleStatusText,
    required this.loadingStatusText,
    required this.failedStatusText,
    required this.finishedStatusText,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if ((status == MasonryLoadMoreStatusState.failed ||
                status == MasonryLoadMoreStatusState.idle) &&
            onRetry != null) {
          onRetry!();
        }
      },
      child: Container(
        height: containerHeight,
        alignment: Alignment.center,
        child: _buildStatusWidget(),
      ),
    );
  }

  Widget _buildStatusWidget() {
    switch (status) {
      case MasonryLoadMoreStatusState.idle:
        return Text(idleStatusText, style: TextStyle(color: Colors.grey[600]));

      case MasonryLoadMoreStatusState.loading:
        return Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: size,
              height: size,
              child: CircularProgressIndicator(
                strokeWidth: strokeWidth,
                valueColor: AlwaysStoppedAnimation<Color>(color),
              ),
            ),
            const SizedBox(width: 12),
            Text(loadingStatusText, style: TextStyle(color: Colors.grey[600])),
          ],
        );

      case MasonryLoadMoreStatusState.failed:
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.error_outline, color: Colors.red[400], size: size),
            const SizedBox(height: 8),
            Text(failedStatusText, style: TextStyle(color: Colors.red[600])),
            const SizedBox(height: 4),
            Text(
              'Tap to retry',
              style: TextStyle(color: Colors.grey[500], fontSize: 12),
            ),
          ],
        );

      case MasonryLoadMoreStatusState.finished:
        return Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle_outline,
              color: Colors.green[400],
              size: size * 0.8,
            ),
            const SizedBox(width: 8),
            Text(finishedStatusText, style: TextStyle(color: Colors.grey[600])),
          ],
        );
    }
  }
}
